import type { NextPage } from 'next';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';

import { userAPI } from '@/APIs';
import AccountDetails from '../../../components/accounting/accountDetails';
import { Account } from 'models/accounting/account.interface';
import { toast } from 'react-toastify';

const AccountView: NextPage = () => {
  const router = useRouter();
  const { id } = router.query;
  const [account, setAccount] = useState<Account | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const getAccountDetails = async () => {
      if (!id || typeof id !== 'string') return;
      
      try {
        setLoading(true);
        setError(null);
        const res = await userAPI.getAccount(id);
        
        if (res.data) {
          setAccount(res.data);
        } else {
          setError('Account not found');
          toast.error('Account not found');
        }
      } catch (error: any) {
        console.error('Error fetching account details:', error);
        setError('Failed to load account details');
        toast.error('Failed to load account details');
      } finally {
        setLoading(false);
      }
    };

    getAccountDetails();
  }, [id]);

  if (loading) {
    return (
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Account Details</div>
          <Link href="/accounting" className="btn btn-outline-secondary">
            <i className="bi bi-arrow-left me-2"></i>
            Back to Accounting
          </Link>
        </div>
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
          <div className="text-center">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-3 text-muted">Loading account details...</p>
          </div>
        </div>
      </main>
    );
  }

  if (error || !account) {
    return (
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Account Details</div>
          <Link href="/accounting" className="btn btn-outline-secondary">
            <i className="bi bi-arrow-left me-2"></i>
            Back to Accounting
          </Link>
        </div>
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
          <div className="text-center">
            <i className="bi bi-exclamation-triangle fs-1 text-warning"></i>
            <h4 className="mt-3">Account Not Found</h4>
            <p className="text-muted">
              {error || 'The requested account could not be found.'}
            </p>
            <Link href="/accounting" className="btn btn-primary">
              <i className="bi bi-arrow-left me-2"></i>
              Back to Accounting
            </Link>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="px-5">
      <div className="d-flex justify-content-between align-items-center mt-3">
        <div className="fs-2">Account Details</div>
        <Link href="/accounting" className="btn btn-outline-secondary">
          <i className="bi bi-arrow-left me-2"></i>
          Back to Accounting
        </Link>
      </div>
      
      <AccountDetails account={account} />
    </main>
  );
};

export default AccountView;
