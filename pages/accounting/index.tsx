import type { NextPage } from 'next';
import { useEffect, useState, useCallback } from 'react';

import { userAPI } from '@/APIs';
import PrevNextPagination from '@/components/common/newPagination';
import AccountsList from '../../components/accounting/list/accountsList';
import { Account } from 'models/accounting/account.interface';
import { toast } from 'react-toastify';

const Accounting: NextPage = () => {
  const [allAccounts, setAllAccounts] = useState<Account[]>([]);
  const [loading, setLoading] = useState(false);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(10); // 10 transactions per page
  const [disableNext, setDisableNext] = useState(false);

  const fetchAccounts = useCallback(async () => {
    setLoading(true);
    try {
      const res = await userAPI.getAccounts(0, 1000); // Fetch all accounts
      if (res.data) {
        setAllAccounts(res.data);
      } else {
        toast.error('Can not get accounts');
      }
    } catch (error) {
      console.log(error);
      toast.error('Failed to fetch accounts');
    } finally {
      setLoading(false);
    }
  }, []);

  // Calculate paginated accounts and pagination state
  const paginatedAccounts = allAccounts.slice(skip, skip + limit);
  const total = allAccounts.length;
  const currentPage = Math.floor(skip / limit) + 1;
  const totalPages = Math.ceil(total / limit);

  // Update disableNext when data changes
  useEffect(() => {
    setDisableNext(skip + limit >= total);
  }, [skip, limit, total]);

  useEffect(() => {
    fetchAccounts();
  }, [fetchAccounts]);

  return (
    <main className="px-5">
      <div className="d-flex justify-content-between align-items-center mt-3">
        <div className="fs-2">Accounting</div>
      </div>

      {/* Accounts Table */}
      <div className="card mt-3">
        <div className="card-body">
          {loading ? (
            <div className="text-center py-4">
              <div className="spinner-border" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </div>
          ) : (
            <>
              {paginatedAccounts.length === 0 ? (
                <div className="text-center py-4">
                  <i className="bi bi-receipt fs-1 text-muted"></i>
                  <p className="text-muted mt-2">No accounting transactions to show</p>
                </div>
              ) : (
                <AccountsList accountsList={paginatedAccounts} />
              )}

              {/* Pagination */}
              {total > limit && (
                <div className="d-flex justify-content-between align-items-center mt-3">
                  <div className="text-muted">
                    Page {currentPage} of {totalPages}
                  </div>
                  <PrevNextPagination
                    skip={skip}
                    setSkip={setSkip}
                    limit={limit}
                    disableNext={disableNext}
                  />
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </main>
  );
};

export default Accounting;
