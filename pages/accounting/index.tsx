import type { NextPage } from 'next';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import PrevNextPagination from '@/components/common/newPagination';
import AccountsList from '@/components/accounting/list/accountsList';
import { Account } from 'models/accounting/account.interface';
import { toast } from 'react-toastify';

const Accounting: NextPage = () => {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(10); // 10 transactions per page
  const [disableNext, setDisableNext] = useState(false);

  useEffect(() => {
    const getAccountsList = async () => {
      try {
        const res = await userAPI.getAccounts(skip, limit);
        if (res.data) {
          if (res.data.length === 0) {
            setDisableNext(true);
            if (skip === 0) setAccounts(res.data);
          } else {
            setDisableNext(res.data.length < limit);
            setAccounts(res.data);
          }
        } else {
          toast.error('Can not get accounts');
        }
      } catch (error) {
        console.log(error);
      }
    };

    getAccountsList();
  }, [skip, limit]);

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Accounting</div>
        </div>
        <div>
          {accounts?.length! > 0 ? (
            <>
              <AccountsList accountsList={accounts!} />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            'There are no accounting transactions'
          )}
        </div>
      </main>
    </>
  );
};

export default Accounting;
