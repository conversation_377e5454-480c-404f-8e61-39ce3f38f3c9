import { FC } from 'react';
import { Account } from 'models/accounting/account.interface';
import Table from '@/components/global/table/table';
import Link from 'next/link';

interface Props {
  accountsList: Account[];
}

const AccountsList: FC<Props> = ({ accountsList }) => {
  const formatAmount = (amount: number) => {
    const formatted = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BDT',
      currencyDisplay: 'code',
    }).format(amount);
    return formatted.replace('BDT', 'TK');
  };

  const getTransactionTypeColor = (type: string) => {
    return type === 'credit' ? 'text-success' : 'text-danger';
  };

  const getTransactionTypeBadge = (type: string) => {
    return type === 'credit' ? 'bg-success' : 'bg-danger';
  };

  const columns = [
    {
      label: 'Type',
      path: 'transactionType',
      content: (data: Account, key: any, index: any) => (
        <td className="text-center align-middle">
          <span className={`badge ${getTransactionTypeBadge(data.transactionType)} fs-6`}>
            {data.transactionType.toUpperCase()}
          </span>
        </td>
      ),
    },
    {
      label: 'Amount',
      path: 'amount',
      content: (data: Account, key: any, index: any) => (
        <td className="text-center align-middle">
          <span className={`fw-bold ${getTransactionTypeColor(data.transactionType)}`}>
            {formatAmount(data.amount)}
          </span>
        </td>
      ),
    },
    {
      label: 'Reference',
      path: 'reference',
      content: (data: Account, key: any, index: any) => (
        <td className="text-center align-middle">
          <div>
            <div className="fw-bold">{data.reference.toUpperCase()}</div>
            <small className="text-muted">{data.referenceType.toUpperCase()}</small>
          </div>
        </td>
      ),
    },
    {
      label: 'Actions',
      path: 'id',
      content: (data: Account, key: any, index: any) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/accounting/view/[id]`,
              query: { id: data.id },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-outline-primary btn-sm">
              <i className="bi bi-eye me-1"></i>
              View Details
            </button>
          </Link>
        </td>
      ),
    },
  ];

  return (
    <>
      {accountsList?.length > 0 ? (
        <div className="table-responsive">
          <table className="table table-striped">
            <thead>
              <tr>
                {columns.map((col, index) => (
                  <th key={index} className="text-center">
                    {col.label}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {accountsList.map((account, index) => (
                <tr key={account.id || index}>
                  {columns.map((col, colIndex) => (
                    <React.Fragment key={colIndex}>
                      {col.content(account, col.path, index)}
                    </React.Fragment>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="text-center py-4">
          <i className="bi bi-receipt fs-1 text-muted"></i>
          <p className="text-muted mt-2">No accounting transactions to show</p>
        </div>
      )}
    </>
  );
};

export default AccountsList;
